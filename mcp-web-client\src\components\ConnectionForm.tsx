'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { TransportConfig, TransportType } from '@/types/mcp';
import { TRANSPORT_EXAMPLES } from '@/lib/mcp/transports';

interface ConnectionFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function ConnectionForm({ onSuccess, onCancel }: ConnectionFormProps) {
  const { createConnection, connectToServer } = useMCPClientContext();
  
  const [formData, setFormData] = useState({
    name: '',
    type: 'stdio' as TransportType,
    url: '',
    command: '',
    args: '',
    env: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validar campos requeridos
      if (!formData.name.trim()) {
        throw new Error('El nombre es requerido');
      }

      // Construir configuración del transporte
      const config: TransportConfig = {
        type: formData.type
      };

      if (formData.type === 'stdio') {
        if (!formData.command.trim()) {
          throw new Error('El comando es requerido para conexiones STDIO');
        }
        config.command = formData.command.trim();
        config.args = formData.args.trim() ? formData.args.trim().split(' ') : [];
        config.env = formData.env.trim() ? JSON.parse(formData.env) : {};
      } else {
        if (!formData.url.trim()) {
          throw new Error('La URL es requerida para conexiones HTTP/SSE');
        }
        config.url = formData.url.trim();
      }

      // Crear conexión
      const connectionId = await createConnection(formData.name.trim(), config);

      // Esperar un momento para que la conexión se registre en el estado
      await new Promise(resolve => setTimeout(resolve, 100));

      // Intentar conectar automáticamente
      try {
        await connectToServer(connectionId);
      } catch (connectError) {
        console.warn('Failed to auto-connect:', connectError);
        // No fallar si la conexión automática falla
      }

      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const loadPreset = (preset: any) => {
    setFormData({
      name: '',
      type: preset.type,
      url: preset.url || '',
      command: preset.command || '',
      args: preset.args ? preset.args.join(' ') : '',
      env: preset.env ? JSON.stringify(preset.env, null, 2) : ''
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="text-red-700 dark:text-red-300 text-sm">
            {error}
          </div>
        </div>
      )}

      {/* Nombre de la conexión */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Nombre de la conexión
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Mi servidor MCP"
          required
        />
      </div>

      {/* Tipo de transporte */}
      <div>
        <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Tipo de transporte
        </label>
        <select
          id="type"
          value={formData.type}
          onChange={(e) => setFormData({ ...formData, type: e.target.value as TransportType })}
          className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="stdio">STDIO (Proceso local)</option>
          <option value="sse">SSE (Server-Sent Events)</option>
          <option value="streamable-http">HTTP Streamable</option>
        </select>
      </div>

      {/* Configuración específica del transporte */}
      {formData.type === 'stdio' ? (
        <>
          <div>
            <label htmlFor="command" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Comando
            </label>
            <input
              type="text"
              id="command"
              value={formData.command}
              onChange={(e) => setFormData({ ...formData, command: e.target.value })}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="npx, python, node, etc."
              required
            />
          </div>
          
          <div>
            <label htmlFor="args" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Argumentos (separados por espacios)
            </label>
            <input
              type="text"
              id="args"
              value={formData.args}
              onChange={(e) => setFormData({ ...formData, args: e.target.value })}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="-y @modelcontextprotocol/server-memory"
            />
          </div>
          
          <div>
            <label htmlFor="env" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Variables de entorno (JSON)
            </label>
            <textarea
              id="env"
              value={formData.env}
              onChange={(e) => setFormData({ ...formData, env: e.target.value })}
              rows={3}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder='{"API_KEY": "valor"}'
            />
          </div>
        </>
      ) : (
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            URL del servidor
          </label>
          <input
            type="url"
            id="url"
            value={formData.url}
            onChange={(e) => setFormData({ ...formData, url: e.target.value })}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="http://localhost:3000/mcp"
            required
          />
        </div>
      )}

      {/* Presets */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Configuraciones predefinidas
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {formData.type === 'stdio' && (
            <>
              <button
                type="button"
                onClick={() => loadPreset(TRANSPORT_EXAMPLES.stdio.filesystem)}
                className="text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
              >
                <div className="font-medium">Filesystem</div>
                <div className="text-gray-500 dark:text-gray-400">Acceso al sistema de archivos</div>
              </button>
              <button
                type="button"
                onClick={() => loadPreset(TRANSPORT_EXAMPLES.stdio.memory)}
                className="text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
              >
                <div className="font-medium">Memory</div>
                <div className="text-gray-500 dark:text-gray-400">Almacenamiento en memoria</div>
              </button>
            </>
          )}
          {(formData.type === 'sse' || formData.type === 'streamable-http') && (
            <>
              <button
                type="button"
                onClick={() => loadPreset(formData.type === 'sse' ? TRANSPORT_EXAMPLES.sse.local : TRANSPORT_EXAMPLES.http.local)}
                className="text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
              >
                <div className="font-medium">Servidor local</div>
                <div className="text-gray-500 dark:text-gray-400">localhost:3000</div>
              </button>
            </>
          )}
        </div>
      </div>

      {/* Botones */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          Cancelar
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md transition-colors"
        >
          {loading ? 'Creando...' : 'Crear Conexión'}
        </button>
      </div>
    </form>
  );
}
