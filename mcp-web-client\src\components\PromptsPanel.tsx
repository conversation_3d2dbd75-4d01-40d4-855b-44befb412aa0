'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { Prompt, GetPromptResult } from '@/types/mcp';

export function PromptsPanel() {
  const { getActiveConnection, getPrompt } = useMCPClientContext();
  const [selectedPrompt, setSelectedPrompt] = useState<Prompt | null>(null);
  const [promptArgs, setPromptArgs] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<GetPromptResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const activeConnection = getActiveConnection();
  const prompts = activeConnection?.prompts || [];

  const handlePromptSelect = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
    setResult(null);
    setError(null);
    
    // Inicializar argumentos
    const initialArgs: Record<string, string> = {};
    if (prompt.arguments) {
      prompt.arguments.forEach(arg => {
        initialArgs[arg.name] = '';
      });
    }
    setPromptArgs(initialArgs);
  };

  const handleExecutePrompt = async () => {
    if (!selectedPrompt || !activeConnection) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const promptResult = await getPrompt(activeConnection.id, {
        name: selectedPrompt.name,
        arguments: promptArgs
      });
      setResult(promptResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  if (!activeConnection) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Selecciona una conexión activa para ver los prompts disponibles.
        </p>
      </div>
    );
  }

  if (prompts.length === 0) {
    return (
      <div className="text-center py-8">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No hay prompts disponibles
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          El servidor conectado no expone ningún prompt.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Lista de prompts */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Prompts Disponibles ({prompts.length})
        </h3>
        <div className="space-y-3">
          {prompts.map((prompt) => (
            <div
              key={prompt.name}
              onClick={() => handlePromptSelect(prompt)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedPrompt?.name === prompt.name
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <h4 className="font-medium text-gray-900 dark:text-white">
                {prompt.name}
              </h4>
              {prompt.description && (
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {prompt.description}
                </p>
              )}
              {prompt.arguments && prompt.arguments.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Argumentos: {prompt.arguments.map(arg => arg.name).join(', ')}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Panel de ejecución */}
      <div>
        {selectedPrompt ? (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Prompt: {selectedPrompt.name}
            </h3>
            
            {selectedPrompt.description && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                {selectedPrompt.description}
              </p>
            )}

            {/* Formulario de argumentos */}
            {selectedPrompt.arguments && selectedPrompt.arguments.length > 0 && (
              <div className="space-y-4 mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white">Argumentos</h4>
                {selectedPrompt.arguments.map((arg) => (
                  <div key={arg.name}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {arg.name}
                      {arg.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    <input
                      type="text"
                      value={promptArgs[arg.name] || ''}
                      onChange={(e) => setPromptArgs({ ...promptArgs, [arg.name]: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={arg.description || `Ingrese ${arg.name}`}
                      required={arg.required}
                    />
                    {arg.description && (
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {arg.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Botón de ejecución */}
            <button
              onClick={handleExecutePrompt}
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              {loading ? 'Generando...' : 'Generar Prompt'}
            </button>

            {/* Resultados */}
            {error && (
              <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <h4 className="font-medium text-red-800 dark:text-red-300 mb-2">Error</h4>
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}

            {result && (
              <div className="mt-4 space-y-4">
                {result.description && (
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1">Descripción</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">{result.description}</p>
                  </div>
                )}
                
                <div className="p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Mensajes Generados</h4>
                  <div className="space-y-3">
                    {result.messages.map((message, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                        <div className="bg-gray-100 dark:bg-gray-700 px-3 py-2">
                          <span className={`text-sm font-medium ${
                            message.role === 'user' ? 'text-blue-600 dark:text-blue-400' :
                            message.role === 'assistant' ? 'text-green-600 dark:text-green-400' :
                            'text-purple-600 dark:text-purple-400'
                          }`}>
                            {message.role.charAt(0).toUpperCase() + message.role.slice(1)}
                          </span>
                        </div>
                        <div className="p-3">
                          {message.content.type === 'text' && (
                            <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                              {message.content.text}
                            </p>
                          )}
                          {message.content.type === 'image' && (
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">Imagen:</p>
                              <img 
                                src={`data:${message.content.mimeType};base64,${message.content.data}`}
                                alt="Prompt content"
                                className="max-w-full h-auto rounded"
                              />
                            </div>
                          )}
                          {message.content.type === 'resource' && (
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-300">
                                Recurso: {message.content.text}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
              Selecciona un prompt para generarlo.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
