import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import {
  MCPMessage,
  MCPRequest,
  MCPResponse,
  MCPNotification,
  InitializeRequest,
  InitializeResult,
  Tool,
  Resource,
  Prompt,
  CallToolRequest,
  CallToolResult,
  ReadResourceRequest,
  ReadResourceResult,
  GetPromptRequest,
  GetPromptResult,
  LoggingMessageNotification,
  ServerInfo,
  ClientInfo,
  ClientCapabilities,
  TransportConfig,
  MCPClientEvents
} from '@/types/mcp';

export abstract class BaseMCPTransport extends EventEmitter {
  protected connected = false;
  protected connecting = false;

  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract send(message: MCPMessage): Promise<void>;

  isConnected(): boolean {
    return this.connected;
  }

  isConnecting(): boolean {
    return this.connecting;
  }

  protected handleMessage(message: MCPMessage): void {
    this.emit('message', message);
  }

  protected handleError(error: Error): void {
    this.emit('error', error);
  }

  protected handleConnect(): void {
    this.connected = true;
    this.connecting = false;
    this.emit('connect');
  }

  protected handleDisconnect(): void {
    this.connected = false;
    this.connecting = false;
    this.emit('disconnect');
  }
}

export class MCPClient extends EventEmitter {
  private transport: BaseMCPTransport | null = null;
  private pendingRequests = new Map<string | number, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  private requestTimeout = 30000; // 30 segundos
  private serverInfo: ServerInfo | null = null;
  private clientInfo: ClientInfo;
  private capabilities: ClientCapabilities;
  private tools: Tool[] = [];
  private resources: Resource[] = [];
  private prompts: Prompt[] = [];
  private initialized = false;

  constructor(clientInfo: ClientInfo, capabilities: ClientCapabilities = {}) {
    super();
    this.clientInfo = clientInfo;
    this.capabilities = capabilities;
  }

  async connect(transport: BaseMCPTransport): Promise<void> {
    if (this.transport) {
      await this.disconnect();
    }

    this.transport = transport;
    this.setupTransportListeners();

    try {
      await this.transport.connect();
      await this.initialize();
    } catch (error) {
      this.transport = null;
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.transport) {
      this.removeTransportListeners();
      await this.transport.disconnect();
      this.transport = null;
    }
    this.initialized = false;
    this.serverInfo = null;
    this.tools = [];
    this.resources = [];
    this.prompts = [];
    this.clearPendingRequests();
  }

  private setupTransportListeners(): void {
    if (!this.transport) return;

    this.transport.on('message', this.handleMessage.bind(this));
    this.transport.on('error', this.handleTransportError.bind(this));
    this.transport.on('disconnect', this.handleTransportDisconnect.bind(this));
  }

  private removeTransportListeners(): void {
    if (!this.transport) return;

    this.transport.removeAllListeners('message');
    this.transport.removeAllListeners('error');
    this.transport.removeAllListeners('disconnect');
  }

  private handleMessage(message: MCPMessage): void {
    if (message.id !== undefined) {
      // Es una respuesta a una solicitud
      const pending = this.pendingRequests.get(message.id);
      if (pending) {
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(message.id);

        if (message.error) {
          pending.reject(new Error(`MCP Error ${message.error.code}: ${message.error.message}`));
        } else {
          pending.resolve(message.result);
        }
      }
    } else if (message.method) {
      // Es una notificación
      this.handleNotification(message as MCPNotification);
    }

    this.emit('message', message);
  }

  private handleNotification(notification: MCPNotification): void {
    switch (notification.method) {
      case 'notifications/message':
        this.emit('log', notification.params as LoggingMessageNotification);
        break;
      case 'notifications/tools/list_changed':
        this.refreshTools();
        break;
      case 'notifications/resources/list_changed':
        this.refreshResources();
        break;
      case 'notifications/prompts/list_changed':
        this.refreshPrompts();
        break;
    }
  }

  private handleTransportError(error: Error): void {
    this.emit('error', error);
  }

  private handleTransportDisconnect(): void {
    this.initialized = false;
    this.emit('disconnect');
  }

  private async sendRequest(method: string, params?: any): Promise<any> {
    if (!this.transport || !this.transport.isConnected()) {
      throw new Error('No hay conexión activa');
    }

    const id = uuidv4();
    const request: MCPRequest = {
      jsonrpc: "2.0",
      id,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(id);
        reject(new Error(`Timeout: No se recibió respuesta para ${method}`));
      }, this.requestTimeout);

      this.pendingRequests.set(id, { resolve, reject, timeout });
      this.transport!.send(request).catch(reject);
    });
  }

  private async initialize(): Promise<void> {
    const initRequest: InitializeRequest = {
      protocolVersion: "2024-11-05",
      capabilities: this.capabilities,
      clientInfo: this.clientInfo
    };

    console.log('Sending initialize request:', initRequest);

    let result: InitializeResult;
    try {
      result = await this.sendRequest('initialize', initRequest);
      console.log('Initialize response:', result);
      this.serverInfo = result.serverInfo;
    } catch (error) {
      console.error('Initialize failed:', error);
      throw error;
    }

    // Enviar notificación de inicialización completada
    await this.sendNotification('notifications/initialized');

    // Cargar listas iniciales (solo si el servidor las soporta)
    // Hacerlo secuencialmente para evitar problemas de concurrencia
    try {
      await this.refreshTools();
    } catch (error) {
      console.warn('Server does not support tools:', error);
    }

    try {
      await this.refreshResources();
    } catch (error) {
      console.warn('Server does not support resources:', error);
    }

    try {
      await this.refreshPrompts();
    } catch (error) {
      console.warn('Server does not support prompts:', error);
    }

    this.initialized = true;
    this.emit('initialized', result);
  }

  private async sendNotification(method: string, params?: any): Promise<void> {
    if (!this.transport || !this.transport.isConnected()) {
      throw new Error('No hay conexión activa');
    }

    const notification: MCPNotification = {
      jsonrpc: "2.0",
      method,
      params
    };

    await this.transport.send(notification);
  }

  private async refreshTools(): Promise<void> {
    try {
      const result = await this.sendRequest('tools/list');
      this.tools = result.tools || [];
      this.emit('toolsChanged', this.tools);
    } catch (error) {
      // Si el método no existe, simplemente no hay herramientas
      if (error instanceof Error && error.message.includes('-32601')) {
        console.log('Server does not support tools');
        this.tools = [];
        this.emit('toolsChanged', this.tools);
      } else {
        console.error('Error refreshing tools:', error);
        throw error;
      }
    }
  }

  private async refreshResources(): Promise<void> {
    try {
      const result = await this.sendRequest('resources/list');
      this.resources = result.resources || [];
      this.emit('resourcesChanged', this.resources);
    } catch (error) {
      // Si el método no existe, simplemente no hay recursos
      if (error instanceof Error && error.message.includes('-32601')) {
        console.log('Server does not support resources');
        this.resources = [];
        this.emit('resourcesChanged', this.resources);
      } else {
        console.error('Error refreshing resources:', error);
        throw error;
      }
    }
  }

  private async refreshPrompts(): Promise<void> {
    try {
      const result = await this.sendRequest('prompts/list');
      this.prompts = result.prompts || [];
      this.emit('promptsChanged', this.prompts);
    } catch (error) {
      // Si el método no existe, simplemente no hay prompts
      if (error instanceof Error && error.message.includes('-32601')) {
        console.log('Server does not support prompts');
        this.prompts = [];
        this.emit('promptsChanged', this.prompts);
      } else {
        console.error('Error refreshing prompts:', error);
        throw error;
      }
    }
  }

  private clearPendingRequests(): void {
    for (const [, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Conexión cerrada'));
    }
    this.pendingRequests.clear();
  }

  // Métodos públicos de la API
  isInitialized(): boolean {
    return this.initialized;
  }

  isConnected(): boolean {
    return this.transport?.isConnected() || false;
  }

  getServerInfo(): ServerInfo | null {
    return this.serverInfo;
  }

  getTools(): Tool[] {
    return [...this.tools];
  }

  getResources(): Resource[] {
    return [...this.resources];
  }

  getPrompts(): Prompt[] {
    return [...this.prompts];
  }

  async callTool(request: CallToolRequest): Promise<CallToolResult> {
    if (!this.initialized) {
      throw new Error('Cliente no inicializado');
    }

    const result = await this.sendRequest('tools/call', request);
    this.emit('toolResult', request.name, result);
    return result;
  }

  async readResource(request: ReadResourceRequest): Promise<ReadResourceResult> {
    if (!this.initialized) {
      throw new Error('Cliente no inicializado');
    }

    return await this.sendRequest('resources/read', request);
  }

  async getPrompt(request: GetPromptRequest): Promise<GetPromptResult> {
    if (!this.initialized) {
      throw new Error('Cliente no inicializado');
    }

    return await this.sendRequest('prompts/get', request);
  }

  async setLoggingLevel(level: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('Cliente no inicializado');
    }

    await this.sendNotification('notifications/logging/setLevel', { level });
  }
}
