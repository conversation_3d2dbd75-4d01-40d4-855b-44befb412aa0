'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { ConnectionList } from './ConnectionList';
import { ConnectionForm } from './ConnectionForm';
import { ToolsPanel } from './ToolsPanel';
import { ResourcesPanel } from './ResourcesPanel';
import { PromptsPanel } from './PromptsPanel';
import { LogsPanel } from './LogsPanel';

type ActiveTab = 'connections' | 'tools' | 'resources' | 'prompts' | 'logs';

export function MCPDashboard() {
  const [activeTab, setActiveTab] = useState<ActiveTab>('connections');
  const [showConnectionForm, setShowConnectionForm] = useState(false);
  const { connections, activeConnection, getActiveConnection } = useMCPClientContext();

  const activeConn = getActiveConnection();
  const connectedCount = connections.filter(c => c.status.connected).length;

  const tabs = [
    { id: 'connections' as const, label: 'Conexiones', count: connections.length },
    { id: 'tools' as const, label: 'Herramientas', count: activeConn?.tools.length || 0 },
    { id: 'resources' as const, label: 'Recursos', count: activeConn?.resources.length || 0 },
    { id: 'prompts' as const, label: 'Prompts', count: activeConn?.prompts.length || 0 },
    { id: 'logs' as const, label: 'Logs', count: 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Estado general */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Estado del Cliente MCP
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {connectedCount} de {connections.length} conexiones activas
              {activeConnection && (
                <span className="ml-2">
                  • Conexión activa: <span className="font-medium">{activeConn?.name}</span>
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${connectedCount > 0 ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {connectedCount > 0 ? 'Conectado' : 'Desconectado'}
              </span>
            </div>
            <button
              onClick={() => setShowConnectionForm(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Nueva Conexión
            </button>
          </div>
        </div>
      </div>

      {/* Navegación por pestañas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                {tab.label}
                {tab.count > 0 && (
                  <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenido de las pestañas */}
        <div className="p-6">
          {activeTab === 'connections' && <ConnectionList />}
          {activeTab === 'tools' && <ToolsPanel />}
          {activeTab === 'resources' && <ResourcesPanel />}
          {activeTab === 'prompts' && <PromptsPanel />}
          {activeTab === 'logs' && <LogsPanel />}
        </div>
      </div>

      {/* Modal para nueva conexión */}
      {showConnectionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Nueva Conexión MCP
              </h3>
              <button
                onClick={() => setShowConnectionForm(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              <ConnectionForm
                onSuccess={() => {
                  setShowConnectionForm(false);
                  setActiveTab('connections');
                }}
                onCancel={() => setShowConnectionForm(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
