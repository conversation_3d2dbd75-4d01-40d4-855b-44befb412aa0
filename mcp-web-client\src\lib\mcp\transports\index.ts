// Exportar todos los transportes MCP
import { BaseMCPTransport } from '../base-client';
export { BaseMCPTransport };

// STDIO Transport
export {
  StdioTransport,
  createStdioTransport,
  STDIO_PRESETS,
  type StdioTransportConfig
} from './stdio-transport';

// SSE Transport
export {
  SSETransport,
  createSSETransport,
  detectSSESupport,
  SSE_PRESETS,
  type SSETransportConfig
} from './sse-transport';

// HTTP Transport (Streamable)
export {
  HttpTransport,
  createHttpTransport,
  detectTransportType,
  HTTP_PRESETS,
  type HttpTransportConfig
} from './http-transport';

// Función helper para crear transporte automáticamente
import { TransportConfig, TransportType } from '@/types/mcp';
import { StdioTransport } from './stdio-transport';
import { SSETransport } from './sse-transport';
import { HttpTransport, detectTransportType } from './http-transport';

export function createTransport(config: TransportConfig): BaseMCPTransport {
  switch (config.type) {
    case 'stdio':
      return new StdioTransport(config as any);
    case 'sse':
      return new SSETransport(config as any);
    case 'http':
    case 'streamable-http':
      return new HttpTransport(config as any);
    default:
      throw new Error(`Unsupported transport type: ${config.type}`);
  }
}

// Función para detectar automáticamente el mejor transporte para una URL
export async function detectBestTransport(url: string): Promise<TransportType> {
  try {
    const httpType = await detectTransportType(url);
    if (httpType === 'streamable-http') {
      return 'streamable-http';
    } else if (httpType === 'sse') {
      return 'sse';
    }
    
    // Si no es HTTP, asumir que es para uso local con stdio
    return 'stdio';
  } catch {
    // Por defecto, usar stdio para conexiones locales
    return 'stdio';
  }
}

// Configuraciones de ejemplo para diferentes tipos de servidores
export const TRANSPORT_EXAMPLES = {
  stdio: {
    filesystem: {
      type: 'stdio' as const,
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-filesystem', '/path/to/directory']
    },
    memory: {
      type: 'stdio' as const,
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-memory']
    },
    python: {
      type: 'stdio' as const,
      command: 'python',
      args: ['server.py']
    }
  },
  
  sse: {
    local: {
      type: 'sse' as const,
      url: 'http://localhost:3000/sse'
    },
    remote: {
      type: 'sse' as const,
      url: 'https://example.com/mcp/sse'
    }
  },
  
  http: {
    local: {
      type: 'streamable-http' as const,
      url: 'http://localhost:3000/mcp'
    },
    remote: {
      type: 'streamable-http' as const,
      url: 'https://api.example.com/mcp'
    }
  }
} as const;
