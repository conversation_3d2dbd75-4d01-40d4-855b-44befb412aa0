{"name": "mcp-web-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "proxy:install": "cd . && npm install --prefix . express ws cors nodemon", "proxy:start": "node proxy-server.js", "proxy:dev": "npx nodemon proxy-server.js", "dev:full": "concurrently \"npm run proxy:dev\" \"npm run dev\"", "setup": "npm install && npm run proxy:install"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "cors": "^2.8.5", "express": "^5.1.0", "next": "15.3.4", "nodemon": "^3.1.10", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}