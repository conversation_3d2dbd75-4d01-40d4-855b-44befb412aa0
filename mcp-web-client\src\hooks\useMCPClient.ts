import { useState, useEffect, useCallback, useRef } from 'react';
import { MCPClient } from '@/lib/mcp/base-client';
import { createTransport } from '@/lib/mcp/transports';
import {
  MCPConnection,
  MCPClientState,
  TransportConfig,
  Tool,
  Resource,
  Prompt,
  CallToolRequest,
  CallToolResult,
  ReadResourceRequest,
  ReadResourceResult,
  GetPromptRequest,
  GetPromptResult,
  LogEntry,
  ServerInfo,
  LoggingMessageNotification
} from '@/types/mcp';
import { v4 as uuidv4 } from 'uuid';

export function useMCPClient() {
  const [state, setState] = useState<MCPClientState>({
    connections: [],
    logs: []
  });

  const clientsRef = useRef<Map<string, MCPClient>>(new Map());
  const stateRef = useRef(state);

  // Mantener la referencia del estado actualizada
  stateRef.current = state;

  const addLog = useCallback((level: LogEntry['level'], message: string, connectionId?: string, data?: any) => {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      connectionId,
      data
    };

    setState(prev => ({
      ...prev,
      logs: [...prev.logs.slice(-99), logEntry] // Mantener solo los últimos 100 logs
    }));
  }, []);

  const updateConnection = useCallback((connectionId: string, updates: Partial<MCPConnection>) => {
    setState(prev => ({
      ...prev,
      connections: prev.connections.map(conn =>
        conn.id === connectionId ? { ...conn, ...updates } : conn
      )
    }));
  }, []);

  const createConnection = useCallback(async (name: string, config: TransportConfig): Promise<string> => {
    const connectionId = uuidv4();
    
    const newConnection: MCPConnection = {
      id: connectionId,
      name,
      config,
      status: {
        connected: false,
        connecting: false
      },
      tools: [],
      resources: [],
      prompts: []
    };

    setState(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection]
    }));

    addLog('info', `Conexión creada: ${name}`, connectionId);
    return connectionId;
  }, [addLog]);

  const connectToServer = useCallback(async (connectionId: string): Promise<void> => {
    // Usar la referencia del estado para obtener el estado más actual
    let connection = stateRef.current.connections.find(c => c.id === connectionId);

    // Si no se encuentra, esperar un poco y buscar de nuevo (para casos de timing)
    if (!connection) {
      await new Promise(resolve => setTimeout(resolve, 100));
      connection = stateRef.current.connections.find(c => c.id === connectionId);
    }

    if (!connection) {
      throw new Error('Conexión no encontrada');
    }

    if (connection.status.connecting || connection.status.connected) {
      return;
    }

    updateConnection(connectionId, {
      status: { ...connection.status, connecting: true, error: undefined }
    });

    addLog('info', `Conectando a ${connection.name}...`, connectionId);

    try {
      const transport = createTransport(connection.config);
      const client = new MCPClient(
        { name: 'MCP Web Client', version: '1.0.0' },
        { roots: { listChanged: true }, sampling: {} }
      );

      // Configurar event listeners
      client.on('initialized', (result: any) => {
        const serverInfo: ServerInfo = result.serverInfo;
        updateConnection(connectionId, {
          status: {
            connected: true,
            connecting: false,
            lastConnected: new Date(),
            serverInfo
          }
        });
        addLog('info', `Conectado a ${serverInfo.name} v${serverInfo.version}`, connectionId);
      });

      client.on('toolsChanged', (tools: Tool[]) => {
        updateConnection(connectionId, { tools });
        addLog('info', `Herramientas actualizadas: ${tools.length} disponibles`, connectionId);
      });

      client.on('resourcesChanged', (resources: Resource[]) => {
        updateConnection(connectionId, { resources });
        addLog('info', `Recursos actualizados: ${resources.length} disponibles`, connectionId);
      });

      client.on('promptsChanged', (prompts: Prompt[]) => {
        updateConnection(connectionId, { prompts });
        addLog('info', `Prompts actualizados: ${prompts.length} disponibles`, connectionId);
      });

      client.on('toolResult', (toolName: string, result: CallToolResult) => {
        addLog('info', `Herramienta ejecutada: ${toolName}`, connectionId, result);
      });

      client.on('log', (log: LoggingMessageNotification) => {
        addLog(log.level as LogEntry['level'], log.data, connectionId, log);
      });

      client.on('error', (error: Error) => {
        addLog('error', `Error del cliente: ${error.message}`, connectionId);
        updateConnection(connectionId, {
          status: { ...connection.status, connecting: false, error: error.message }
        });
      });

      client.on('disconnect', () => {
        addLog('warn', 'Desconectado del servidor', connectionId);
        updateConnection(connectionId, {
          status: { connected: false, connecting: false }
        });
        clientsRef.current.delete(connectionId);
      });

      // Conectar
      await client.connect(transport);
      clientsRef.current.set(connectionId, client);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', `Error de conexión: ${errorMessage}`, connectionId);
      updateConnection(connectionId, {
        status: { connected: false, connecting: false, error: errorMessage }
      });
      throw error;
    }
  }, [state.connections, updateConnection, addLog]);

  const disconnectFromServer = useCallback(async (connectionId: string): Promise<void> => {
    const client = clientsRef.current.get(connectionId);
    if (client) {
      await client.disconnect();
      clientsRef.current.delete(connectionId);
    }

    updateConnection(connectionId, {
      status: { connected: false, connecting: false },
      tools: [],
      resources: [],
      prompts: []
    });

    addLog('info', 'Desconectado manualmente', connectionId);
  }, [updateConnection, addLog]);

  const removeConnection = useCallback(async (connectionId: string): Promise<void> => {
    await disconnectFromServer(connectionId);
    
    setState(prev => ({
      ...prev,
      connections: prev.connections.filter(c => c.id !== connectionId),
      activeConnection: prev.activeConnection === connectionId ? undefined : prev.activeConnection
    }));

    addLog('info', 'Conexión eliminada', connectionId);
  }, [disconnectFromServer, addLog]);

  const setActiveConnection = useCallback((connectionId: string | undefined) => {
    setState(prev => ({
      ...prev,
      activeConnection: connectionId
    }));
  }, []);

  const callTool = useCallback(async (connectionId: string, request: CallToolRequest): Promise<CallToolResult> => {
    const client = clientsRef.current.get(connectionId);
    if (!client) {
      throw new Error('Cliente no encontrado o no conectado');
    }

    addLog('info', `Ejecutando herramienta: ${request.name}`, connectionId, request.arguments);
    
    try {
      const result = await client.callTool(request);
      addLog('info', `Herramienta completada: ${request.name}`, connectionId, result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', `Error ejecutando herramienta ${request.name}: ${errorMessage}`, connectionId);
      throw error;
    }
  }, [addLog]);

  const readResource = useCallback(async (connectionId: string, request: ReadResourceRequest): Promise<ReadResourceResult> => {
    const client = clientsRef.current.get(connectionId);
    if (!client) {
      throw new Error('Cliente no encontrado o no conectado');
    }

    addLog('info', `Leyendo recurso: ${request.uri}`, connectionId);
    
    try {
      const result = await client.readResource(request);
      addLog('info', `Recurso leído: ${request.uri}`, connectionId);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', `Error leyendo recurso ${request.uri}: ${errorMessage}`, connectionId);
      throw error;
    }
  }, [addLog]);

  const getPrompt = useCallback(async (connectionId: string, request: GetPromptRequest): Promise<GetPromptResult> => {
    const client = clientsRef.current.get(connectionId);
    if (!client) {
      throw new Error('Cliente no encontrado o no conectado');
    }

    addLog('info', `Obteniendo prompt: ${request.name}`, connectionId, request.arguments);
    
    try {
      const result = await client.getPrompt(request);
      addLog('info', `Prompt obtenido: ${request.name}`, connectionId);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', `Error obteniendo prompt ${request.name}: ${errorMessage}`, connectionId);
      throw error;
    }
  }, [addLog]);

  const clearLogs = useCallback(() => {
    setState(prev => ({ ...prev, logs: [] }));
  }, []);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      // Desconectar todos los clientes al desmontar
      clientsRef.current.forEach(async (client) => {
        try {
          await client.disconnect();
        } catch (error) {
          console.error('Error disconnecting client:', error);
        }
      });
      clientsRef.current.clear();
    };
  }, []);

  return {
    // Estado
    connections: state.connections,
    activeConnection: state.activeConnection,
    logs: state.logs,
    
    // Acciones
    createConnection,
    connectToServer,
    disconnectFromServer,
    removeConnection,
    setActiveConnection,
    
    // Operaciones MCP
    callTool,
    readResource,
    getPrompt,
    
    // Utilidades
    clearLogs,
    
    // Helpers
    getActiveConnection: () => state.connections.find(c => c.id === state.activeConnection),
    getConnectedClients: () => state.connections.filter(c => c.status.connected),
    isConnected: (connectionId: string) => {
      const connection = state.connections.find(c => c.id === connectionId);
      return connection?.status.connected || false;
    }
  };
}
