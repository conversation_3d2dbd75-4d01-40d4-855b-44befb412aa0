'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { MCPConnection } from '@/types/mcp';

export function ConnectionList() {
  const {
    connections,
    activeConnection,
    connectToServer,
    disconnectFromServer,
    removeConnection,
    setActiveConnection
  } = useMCPClientContext();

  const [loadingConnections, setLoadingConnections] = useState<Set<string>>(new Set());

  const handleConnect = async (connectionId: string) => {
    setLoadingConnections(prev => new Set(prev).add(connectionId));
    try {
      await connectToServer(connectionId);
    } catch (error) {
      console.error('Error connecting:', error);

      // Mostrar mensaje más específico según el tipo de error
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      if (errorMessage.includes('CORS') || errorMessage.includes('SSE connection')) {
        alert(`Error de conexión: ${errorMessage}\n\nPara conexiones SSE, asegúrate de que:\n1. El servidor esté ejecutándose en la URL especificada\n2. El servidor tenga CORS configurado\n3. El servidor soporte Server-Sent Events`);
      }
    } finally {
      setLoadingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  const handleDisconnect = async (connectionId: string) => {
    setLoadingConnections(prev => new Set(prev).add(connectionId));
    try {
      await disconnectFromServer(connectionId);
    } catch (error) {
      console.error('Error disconnecting:', error);
    } finally {
      setLoadingConnections(prev => {
        const newSet = new Set(prev);
        newSet.delete(connectionId);
        return newSet;
      });
    }
  };

  const handleRemove = async (connectionId: string) => {
    if (confirm('¿Estás seguro de que quieres eliminar esta conexión?')) {
      try {
        await removeConnection(connectionId);
      } catch (error) {
        console.error('Error removing connection:', error);
      }
    }
  };

  const getStatusColor = (connection: MCPConnection) => {
    if (connection.status.connecting) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
    if (connection.status.connected) return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
    if (connection.status.error) return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
    return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
  };

  const getStatusText = (connection: MCPConnection) => {
    if (connection.status.connecting) return 'Conectando...';
    if (connection.status.connected) return 'Conectado';
    if (connection.status.error) return 'Error';
    return 'Desconectado';
  };

  const getTransportIcon = (type: string) => {
    switch (type) {
      case 'stdio':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'sse':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        );
      case 'http':
      case 'streamable-http':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
          </svg>
        );
    }
  };

  if (connections.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No hay conexiones
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Crea una nueva conexión para comenzar a usar MCP.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {connections.map((connection) => (
        <div
          key={connection.id}
          className={`border rounded-lg p-4 transition-all ${
            activeConnection === connection.id
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {getTransportIcon(connection.config.type)}
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {connection.name}
                </h3>
              </div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(connection)}`}>
                {getStatusText(connection)}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {!connection.status.connected && !connection.status.connecting && (
                <button
                  onClick={() => handleConnect(connection.id)}
                  disabled={loadingConnections.has(connection.id)}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                >
                  {loadingConnections.has(connection.id) ? 'Conectando...' : 'Conectar'}
                </button>
              )}
              
              {connection.status.connected && (
                <>
                  <button
                    onClick={() => setActiveConnection(connection.id)}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      activeConnection === connection.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200'
                    }`}
                  >
                    {activeConnection === connection.id ? 'Activa' : 'Activar'}
                  </button>
                  <button
                    onClick={() => handleDisconnect(connection.id)}
                    disabled={loadingConnections.has(connection.id)}
                    className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    {loadingConnections.has(connection.id) ? 'Desconectando...' : 'Desconectar'}
                  </button>
                </>
              )}
              
              <button
                onClick={() => handleRemove(connection.id)}
                className="text-gray-400 hover:text-red-600 dark:hover:text-red-400 p-1"
                title="Eliminar conexión"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="mt-3 text-sm text-gray-600 dark:text-gray-300">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <span className="font-medium">Tipo:</span> {connection.config.type.toUpperCase()}
              </div>
              {connection.config.url && (
                <div>
                  <span className="font-medium">URL:</span> {connection.config.url}
                </div>
              )}
              {connection.config.command && (
                <div>
                  <span className="font-medium">Comando:</span> {connection.config.command}
                  {connection.config.args && connection.config.args.length > 0 && (
                    <span className="ml-1">{connection.config.args.join(' ')}</span>
                  )}
                </div>
              )}
              {connection.status.serverInfo && (
                <div>
                  <span className="font-medium">Servidor:</span> {connection.status.serverInfo.name} v{connection.status.serverInfo.version}
                </div>
              )}
              {connection.status.lastConnected && (
                <div>
                  <span className="font-medium">Última conexión:</span> {connection.status.lastConnected.toLocaleString()}
                </div>
              )}
            </div>
            
            {connection.status.error && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-700 dark:text-red-300 text-xs">
                <span className="font-medium">Error:</span> {connection.status.error}
              </div>
            )}
            
            {connection.status.connected && (
              <div className="mt-2 flex space-x-4 text-xs">
                <span>
                  <span className="font-medium">Herramientas:</span> {connection.tools.length}
                </span>
                <span>
                  <span className="font-medium">Recursos:</span> {connection.resources.length}
                </span>
                <span>
                  <span className="font-medium">Prompts:</span> {connection.prompts.length}
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
