'use client';

import { MC<PERSON>lientProvider } from '@/components/MCPClientProvider';
import { MCPDashboard } from '@/components/MCPDashboard';

export default function Home() {
  return (
    <MCPClientProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">MCP</span>
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                    MCP Web Client
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Model Context Protocol Client
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  v1.0.0
                </span>
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <MCPDashboard />
        </main>
      </div>
    </MCPClientProvider>
  );
}
