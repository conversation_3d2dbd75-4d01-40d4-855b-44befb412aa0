import { BaseMCPTransport } from '../base-client';
import { MCPMessage, TransportConfig } from '@/types/mcp';

export interface SSETransportConfig extends TransportConfig {
  type: 'sse';
  url: string;
  messageEndpoint?: string;
}

export class SSETransport extends BaseMCPTransport {
  private config: SSETransportConfig;
  private eventSource: EventSource | null = null;
  private messageEndpoint: string;
  private sessionId: string | null = null;
  private lastEventId: string | null = null;

  constructor(config: SSETransportConfig) {
    super();
    this.config = config;
    this.messageEndpoint = config.messageEndpoint || this.config.url.replace('/sse', '/messages');
  }

  private async detectMessageEndpoint(): Promise<void> {
    // Intentar diferentes endpoints comunes para mensajes MCP
    const possibleEndpoints = [
      this.messageEndpoint,
      this.config.url.replace('/sse', '/message'),
      this.config.url.replace('/sse', '/mcp'),
      this.config.url.replace('/sse', ''),
      this.config.url + '/message',
      this.config.url + '/messages'
    ];

    for (const endpoint of possibleEndpoints) {
      try {
        const testResponse = await fetch(endpoint, {
          method: 'OPTIONS',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (testResponse.ok || testResponse.status === 405) {
          // 405 Method Not Allowed es OK, significa que el endpoint existe pero no acepta OPTIONS
          console.log('Using message endpoint:', endpoint);
          this.messageEndpoint = endpoint;
          return;
        }
      } catch {
        // Continuar con el siguiente endpoint
      }
    }

    console.warn('Could not detect message endpoint, using default:', this.messageEndpoint);
  }

  async connect(): Promise<void> {
    if (this.connecting || this.connected) {
      return;
    }

    this.connecting = true;

    try {
      // Intentar detectar el endpoint correcto para mensajes
      await this.detectMessageEndpoint();
      await this.establishSSEConnection();
    } catch (error) {
      this.connecting = false;
      throw error;
    }
  }

  private async establishSSEConnection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const url = new URL(this.config.url);

      // Agregar Last-Event-ID si tenemos uno (para reconexión)
      if (this.lastEventId) {
        url.searchParams.set('Last-Event-ID', this.lastEventId);
      }

      try {
        this.eventSource = new EventSource(url.toString());
      } catch (error) {
        reject(new Error(`Failed to create EventSource: ${error}`));
        return;
      }

      const timeout = setTimeout(() => {
        this.cleanup();
        reject(new Error('Timeout connecting to SSE endpoint'));
      }, 10000);

      let connectionEstablished = false;

      this.eventSource.onopen = () => {
        console.log('SSE connection opened');

        // Si no recibimos un evento 'endpoint' en 2 segundos, asumir que la conexión está lista
        setTimeout(() => {
          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }
        }, 2000);
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Guardar el ID del evento para reconexión
          if (event.lastEventId) {
            this.lastEventId = event.lastEventId;
          }

          // Si recibimos cualquier mensaje válido, considerar la conexión establecida
          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }

          this.handleMessage(data);
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      // Listener para el evento 'endpoint' (requerido por el estándar MCP)
      this.eventSource.addEventListener('endpoint', (event: any) => {
        try {
          // El evento endpoint contiene la URL del endpoint de mensajes
          const endpointUrl = event.data.trim();

          // Verificar si es una URL válida o una ruta relativa
          if (endpointUrl.startsWith('/')) {
            // Es una ruta relativa, construir URL completa
            const baseUrl = new URL(this.config.url);
            this.messageEndpoint = `${baseUrl.protocol}//${baseUrl.host}${endpointUrl}`;
          } else if (endpointUrl.startsWith('http')) {
            // Es una URL completa
            this.messageEndpoint = endpointUrl;
          } else {
            // Asumir que es una ruta relativa sin /
            const baseUrl = new URL(this.config.url);
            this.messageEndpoint = `${baseUrl.protocol}//${baseUrl.host}/${endpointUrl}`;
          }

          console.log('Received endpoint from server:', endpointUrl);
          console.log('Using message endpoint:', this.messageEndpoint);

          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }
        } catch (error) {
          console.warn('Error parsing endpoint event:', error);
          // Intentar usar el dato directamente como endpoint
          const endpointUrl = event.data.trim();
          if (endpointUrl) {
            const baseUrl = new URL(this.config.url);
            this.messageEndpoint = `${baseUrl.protocol}//${baseUrl.host}${endpointUrl}`;
            console.log('Using fallback endpoint:', this.messageEndpoint);

            if (!connectionEstablished) {
              clearTimeout(timeout);
              connectionEstablished = true;
              this.handleConnect();
              resolve();
            }
          }
        }
      });

      this.eventSource.addEventListener('message', (event: any) => {
        try {
          const message: MCPMessage = JSON.parse(event.data);
          
          // Guardar el ID del evento para reconexión
          if (event.lastEventId) {
            this.lastEventId = event.lastEventId;
          }
          
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing MCP message from SSE:', error);
        }
      });

      this.eventSource.onerror = () => {
        if (!connectionEstablished) {
          clearTimeout(timeout);
          reject(new Error('Failed to establish SSE connection. Check if the server supports SSE and CORS is configured.'));
          return;
        }

        if (this.eventSource?.readyState === EventSource.CLOSED) {
          this.handleDisconnect();
        } else {
          // Error temporal, intentar reconectar
          this.handleReconnect();
        }
      };
    });
  }

  private handleReconnect(): void {
    if (!this.connected) return;

    console.log('SSE connection lost, attempting to reconnect...');
    
    setTimeout(async () => {
      try {
        await this.establishSSEConnection();
        console.log('SSE reconnection successful');
      } catch (error) {
        console.error('SSE reconnection failed:', error);
        this.handleError(new Error('Failed to reconnect to SSE endpoint'));
      }
    }, 1000);
  }

  async send(message: MCPMessage): Promise<void> {
    if (!this.connected) {
      throw new Error('Not connected to SSE endpoint');
    }

    try {
      console.log('Sending MCP message:', message);
      console.log('Message endpoint:', this.messageEndpoint);
      console.log('Session ID:', this.sessionId);

      // Preparar headers básicos
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Solo agregar session ID si ya lo tenemos
      if (this.sessionId) {
        headers['Mcp-Session-Id'] = this.sessionId;
      }

      const response = await fetch(this.messageEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(message)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        // Intentar leer el cuerpo de la respuesta para más detalles del error
        let errorDetails = response.statusText;
        try {
          const errorBody = await response.text();
          if (errorBody) {
            errorDetails = errorBody;
          }
        } catch {
          // Ignorar errores al leer el cuerpo
        }

        throw new Error(`HTTP ${response.status}: ${errorDetails}`);
      }

      // Verificar si el servidor devolvió un session ID
      const sessionId = response.headers.get('Mcp-Session-Id') || response.headers.get('mcp-session-id');
      if (sessionId) {
        console.log('Received session ID:', sessionId);
        this.sessionId = sessionId;
      }

      // Si la respuesta tiene contenido, procesarlo
      const contentType = response.headers.get('Content-Type');
      if (contentType?.includes('application/json')) {
        const responseData = await response.json();
        console.log('Response data:', responseData);
        if (responseData) {
          this.handleMessage(responseData);
        }
      }

    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error(`Failed to send message: ${error}`);
    }
  }

  async disconnect(): Promise<void> {
    this.cleanup();
    this.handleDisconnect();
  }

  private cleanup(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.sessionId = null;
    this.lastEventId = null;
  }
}

// Función helper para crear transporte SSE
export function createSSETransport(config: Omit<SSETransportConfig, 'type'>): SSETransport {
  return new SSETransport({
    ...config,
    type: 'sse'
  });
}

// Función para detectar si un servidor soporta SSE
export async function detectSSESupport(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    });

    return response.headers.get('Content-Type')?.includes('text/event-stream') || false;
  } catch (error) {
    return false;
  }
}

// Configuraciones predefinidas para servidores SSE
export const SSE_PRESETS = {
  local: (port: number = 3000, path: string = '/sse') => createSSETransport({
    url: `http://localhost:${port}${path}`
  }),
  
  remote: (host: string, port: number = 3000, path: string = '/sse', secure: boolean = false) => 
    createSSETransport({
      url: `${secure ? 'https' : 'http'}://${host}:${port}${path}`
    }),
  
  custom: (url: string, messageEndpoint?: string) => createSSETransport({
    url,
    messageEndpoint
  })
};
