import { BaseMCPTransport } from '../base-client';
import { MCPMessage, TransportConfig } from '@/types/mcp';

export interface StdioTransportConfig extends TransportConfig {
  type: 'stdio';
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

export class StdioTransport extends BaseMCPTransport {
  private config: StdioTransportConfig;
  private process: any = null;
  private messageBuffer = '';

  constructor(config: StdioTransportConfig) {
    super();
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.connecting || this.connected) {
      return;
    }

    this.connecting = true;

    try {
      // En el navegador, necesitamos usar un proxy o API para ejecutar procesos
      // Por ahora, simularemos la conexión para desarrollo
      if (typeof window !== 'undefined') {
        // Estamos en el navegador - necesitamos usar una API proxy
        await this.connectViaBrowserAPI();
      } else {
        // Estamos en Node.js - podemos usar child_process directamente
        await this.connectViaNodeJS();
      }
    } catch (error) {
      this.connecting = false;
      throw error;
    }
  }

  private async connectViaBrowserAPI(): Promise<void> {
    try {
      // Crear conexión WebSocket al proxy del servidor
      const wsUrl = `ws://localhost:3001/mcp-proxy`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        // Enviar configuración del proceso al proxy
        ws.send(JSON.stringify({
          type: 'start_process',
          command: this.config.command,
          args: this.config.args || [],
          env: this.config.env || {}
        }));
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'process_started') {
          this.handleConnect();
        } else if (data.type === 'stdout') {
          this.handleStdout(data.data);
        } else if (data.type === 'stderr') {
          console.warn('MCP Server stderr:', data.data);
        } else if (data.type === 'error') {
          this.handleError(new Error(data.message));
        } else if (data.type === 'exit') {
          this.handleDisconnect();
        }
      };

      ws.onerror = (error) => {
        this.handleError(new Error('WebSocket error: ' + error));
      };

      ws.onclose = () => {
        this.handleDisconnect();
      };

      this.process = ws;

      // Esperar a que se establezca la conexión
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout connecting to MCP proxy'));
        }, 10000);

        const onConnect = () => {
          clearTimeout(timeout);
          this.removeListener('error', onError);
          resolve();
        };

        const onError = (error: Error) => {
          clearTimeout(timeout);
          this.removeListener('connect', onConnect);
          reject(error);
        };

        this.once('connect', onConnect);
        this.once('error', onError);
      });

    } catch (error) {
      throw new Error(`Failed to connect via browser API: ${error}`);
    }
  }

  private async connectViaNodeJS(): Promise<void> {
    try {
      // Verificar si estamos en Node.js
      if (typeof process === 'undefined' || !process.versions?.node) {
        throw new Error('Node.js environment required for STDIO transport');
      }

      const { spawn } = await import('child_process');

      this.process = spawn(this.config.command, this.config.args || [], {
        env: { ...process.env, ...this.config.env },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.process.stdout.on('data', (data: Buffer) => {
        this.handleStdout(data.toString());
      });

      this.process.stderr.on('data', (data: Buffer) => {
        console.warn('MCP Server stderr:', data.toString());
      });

      this.process.on('error', (error: Error) => {
        this.handleError(error);
      });

      this.process.on('exit', (code: number) => {
        if (code !== 0) {
          this.handleError(new Error(`Process exited with code ${code}`));
        } else {
          this.handleDisconnect();
        }
      });

      // Dar tiempo al proceso para inicializarse
      await new Promise(resolve => setTimeout(resolve, 100));
      this.handleConnect();

    } catch (error) {
      throw new Error(`Failed to spawn process: ${error}`);
    }
  }

  private handleStdout(data: string): void {
    this.messageBuffer += data;
    
    // Procesar mensajes completos (delimitados por nueva línea)
    const lines = this.messageBuffer.split('\n');
    this.messageBuffer = lines.pop() || ''; // Mantener la línea incompleta

    for (const line of lines) {
      if (line.trim()) {
        try {
          const message: MCPMessage = JSON.parse(line.trim());
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing MCP message:', error, 'Line:', line);
        }
      }
    }
  }

  async send(message: MCPMessage): Promise<void> {
    if (!this.connected || !this.process) {
      throw new Error('Not connected');
    }

    const messageStr = JSON.stringify(message) + '\n';

    if (typeof window !== 'undefined') {
      // Enviar a través del WebSocket proxy
      this.process.send(JSON.stringify({
        type: 'stdin',
        data: messageStr
      }));
    } else {
      // Enviar directamente al proceso
      this.process.stdin.write(messageStr);
    }
  }

  async disconnect(): Promise<void> {
    if (!this.process) {
      return;
    }

    if (typeof window !== 'undefined') {
      // Cerrar WebSocket
      if (this.process.readyState === WebSocket.OPEN) {
        this.process.send(JSON.stringify({ type: 'stop_process' }));
        this.process.close();
      }
    } else {
      // Terminar proceso
      this.process.kill();
    }

    this.process = null;
    this.messageBuffer = '';
    this.handleDisconnect();
  }
}

// Función helper para crear transporte STDIO
export function createStdioTransport(config: Omit<StdioTransportConfig, 'type'>): StdioTransport {
  return new StdioTransport({
    ...config,
    type: 'stdio'
  });
}

// Configuraciones predefinidas para servidores MCP comunes
export const STDIO_PRESETS = {
  filesystem: (paths: string[]) => createStdioTransport({
    command: 'npx',
    args: ['-y', '@modelcontextprotocol/server-filesystem', ...paths]
  }),
  
  memory: () => createStdioTransport({
    command: 'npx',
    args: ['-y', '@modelcontextprotocol/server-memory']
  }),
  
  brave_search: (apiKey: string) => createStdioTransport({
    command: 'npx',
    args: ['-y', '@modelcontextprotocol/server-brave-search'],
    env: { BRAVE_API_KEY: apiKey }
  }),
  
  python_server: (scriptPath: string) => createStdioTransport({
    command: 'python',
    args: [scriptPath]
  }),
  
  node_server: (scriptPath: string) => createStdioTransport({
    command: 'node',
    args: [scriptPath]
  }),
  
  custom: (command: string, args: string[] = [], env: Record<string, string> = {}) => 
    createStdioTransport({ command, args, env })
};
