'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { Resource, ReadResourceResult } from '@/types/mcp';

export function ResourcesPanel() {
  const { getActiveConnection, readResource } = useMCPClientContext();
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ReadResourceResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const activeConnection = getActiveConnection();
  const resources = activeConnection?.resources || [];

  const handleResourceSelect = async (resource: Resource) => {
    if (!activeConnection) return;

    setSelectedResource(resource);
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const resourceResult = await readResource(activeConnection.id, { uri: resource.uri });
      setResult(resourceResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  if (!activeConnection) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Selecciona una conexión activa para ver los recursos disponibles.
        </p>
      </div>
    );
  }

  if (resources.length === 0) {
    return (
      <div className="text-center py-8">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No hay recursos disponibles
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          El servidor conectado no expone ningún recurso.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Lista de recursos */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Recursos Disponibles ({resources.length})
        </h3>
        <div className="space-y-3">
          {resources.map((resource) => (
            <div
              key={resource.uri}
              onClick={() => handleResourceSelect(resource)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedResource?.uri === resource.uri
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <h4 className="font-medium text-gray-900 dark:text-white">
                {resource.name}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 font-mono">
                {resource.uri}
              </p>
              {resource.description && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {resource.description}
                </p>
              )}
              {resource.mimeType && (
                <span className="inline-block mt-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 rounded">
                  {resource.mimeType}
                </span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Panel de contenido */}
      <div>
        {selectedResource ? (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {selectedResource.name}
            </h3>
            
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium">URI:</span> {selectedResource.uri}
              </p>
              {selectedResource.mimeType && (
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  <span className="font-medium">Tipo:</span> {selectedResource.mimeType}
                </p>
              )}
              {selectedResource.description && (
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  <span className="font-medium">Descripción:</span> {selectedResource.description}
                </p>
              )}
            </div>

            {loading && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Cargando recurso...</p>
              </div>
            )}

            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <h4 className="font-medium text-red-800 dark:text-red-300 mb-2">Error</h4>
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}

            {result && (
              <div className="space-y-4">
                {result.contents.map((content, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Contenido {index + 1}
                        {content.mimeType && (
                          <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                            ({content.mimeType})
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="p-4">
                      {content.text && (
                        <div>
                          {content.mimeType?.includes('json') ? (
                            <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                              {JSON.stringify(JSON.parse(content.text), null, 2)}
                            </pre>
                          ) : content.mimeType?.includes('html') ? (
                            <div className="prose dark:prose-invert max-w-none">
                              <div dangerouslySetInnerHTML={{ __html: content.text }} />
                            </div>
                          ) : (
                            <pre className="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto whitespace-pre-wrap">
                              {content.text}
                            </pre>
                          )}
                        </div>
                      )}
                      {content.blob && (
                        <div>
                          {content.mimeType?.startsWith('image/') ? (
                            <img 
                              src={`data:${content.mimeType};base64,${content.blob}`}
                              alt="Resource content"
                              className="max-w-full h-auto rounded"
                            />
                          ) : (
                            <div className="text-center py-4">
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                Contenido binario ({content.mimeType})
                              </p>
                              <button
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = `data:${content.mimeType};base64,${content.blob}`;
                                  link.download = selectedResource.name || 'resource';
                                  link.click();
                                }}
                                className="mt-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                              >
                                Descargar
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
              Selecciona un recurso para ver su contenido.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
