// Tipos TypeScript para Model Context Protocol (MCP)

export interface MCPMessage {
  jsonrpc: "2.0";
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

export interface MCPRequest extends MCPMessage {
  method: string;
  params?: any;
}

export interface MCPResponse extends MCPMessage {
  result?: any;
  error?: MCPError;
}

export interface MCPNotification extends MCPMessage {
  method: string;
  params?: any;
}

// Tipos para inicialización
export interface InitializeRequest {
  protocolVersion: string;
  capabilities: ClientCapabilities;
  clientInfo: ClientInfo;
}

export interface InitializeResult {
  protocolVersion: string;
  capabilities: ServerCapabilities;
  serverInfo: ServerInfo;
  instructions?: string;
}

export interface ClientInfo {
  name: string;
  version: string;
  title?: string;
}

export interface ServerInfo {
  name: string;
  version: string;
  title?: string;
}

export interface ClientCapabilities {
  roots?: {
    listChanged?: boolean;
  };
  sampling?: {};
  elicitation?: {};
}

export interface ServerCapabilities {
  logging?: {};
  prompts?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  tools?: {
    listChanged?: boolean;
  };
}

// Tipos para herramientas
export interface Tool {
  name: string;
  description?: string;
  inputSchema: ToolInputSchema;
}

export interface ToolInputSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
}

export interface CallToolRequest {
  name: string;
  arguments?: Record<string, any>;
}

export interface CallToolResult {
  content: ToolContent[];
  isError?: boolean;
}

export interface ToolContent {
  type: "text" | "image" | "resource";
  text?: string;
  data?: string;
  mimeType?: string;
}

// Tipos para recursos
export interface Resource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface ReadResourceRequest {
  uri: string;
}

export interface ReadResourceResult {
  contents: ResourceContent[];
}

export interface ResourceContent {
  uri: string;
  mimeType?: string;
  text?: string;
  blob?: string;
}

// Tipos para prompts
export interface Prompt {
  name: string;
  description?: string;
  arguments?: PromptArgument[];
}

export interface PromptArgument {
  name: string;
  description?: string;
  required?: boolean;
}

export interface GetPromptRequest {
  name: string;
  arguments?: Record<string, string>;
}

export interface GetPromptResult {
  description?: string;
  messages: PromptMessage[];
}

export interface PromptMessage {
  role: "user" | "assistant" | "system";
  content: MessageContent;
}

export interface MessageContent {
  type: "text" | "image" | "resource";
  text?: string;
  data?: string;
  mimeType?: string;
}

// Tipos para logging
export interface LoggingMessageNotification {
  level: "debug" | "info" | "notice" | "warning" | "error" | "critical" | "alert" | "emergency";
  data: any;
  logger?: string;
}

// Tipos para transporte
export type TransportType = "stdio" | "sse" | "http" | "streamable-http";

export interface TransportConfig {
  type: TransportType;
  url?: string;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
}

export interface ConnectionStatus {
  connected: boolean;
  connecting: boolean;
  error?: string;
  lastConnected?: Date;
  serverInfo?: ServerInfo;
}

// Tipos para la aplicación
export interface MCPConnection {
  id: string;
  name: string;
  config: TransportConfig;
  status: ConnectionStatus;
  tools: Tool[];
  resources: Resource[];
  prompts: Prompt[];
}

export interface MCPClientState {
  connections: MCPConnection[];
  activeConnection?: string;
  logs: LogEntry[];
}

export interface LogEntry {
  timestamp: Date;
  level: "debug" | "info" | "warn" | "error";
  message: string;
  connectionId?: string;
  data?: any;
}

// Eventos del cliente MCP
export interface MCPClientEvents {
  connected: (connectionId: string, serverInfo: ServerInfo) => void;
  disconnected: (connectionId: string, error?: string) => void;
  error: (connectionId: string, error: string) => void;
  message: (connectionId: string, message: MCPMessage) => void;
  toolResult: (connectionId: string, toolName: string, result: CallToolResult) => void;
  log: (connectionId: string, log: LoggingMessageNotification) => void;
}
