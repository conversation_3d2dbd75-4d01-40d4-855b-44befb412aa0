'use client';

import React, { useState } from 'react';
import { useMCPClientContext } from './MCPClientProvider';
import { Tool, CallToolRequest, CallToolResult } from '@/types/mcp';

export function ToolsPanel() {
  const { getActiveConnection, callTool } = useMCPClientContext();
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [toolArgs, setToolArgs] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<CallToolResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const activeConnection = getActiveConnection();
  const tools = activeConnection?.tools || [];

  const handleToolSelect = (tool: Tool) => {
    setSelectedTool(tool);
    setResult(null);
    setError(null);
    
    // Inicializar argumentos con valores por defecto
    const initialArgs: Record<string, any> = {};
    if (tool.inputSchema.properties) {
      Object.keys(tool.inputSchema.properties).forEach(key => {
        const property = tool.inputSchema.properties![key];
        if (property.default !== undefined) {
          initialArgs[key] = property.default;
        } else if (property.type === 'string') {
          initialArgs[key] = '';
        } else if (property.type === 'number') {
          initialArgs[key] = 0;
        } else if (property.type === 'boolean') {
          initialArgs[key] = false;
        }
      });
    }
    setToolArgs(initialArgs);
  };

  const handleExecuteTool = async () => {
    if (!selectedTool || !activeConnection) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const request: CallToolRequest = {
        name: selectedTool.name,
        arguments: toolArgs
      };

      const toolResult = await callTool(activeConnection.id, request);
      setResult(toolResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const renderInputField = (key: string, property: any) => {
    const isRequired = selectedTool?.inputSchema.required?.includes(key) || false;
    
    switch (property.type) {
      case 'string':
        if (property.enum) {
          return (
            <select
              value={toolArgs[key] || ''}
              onChange={(e) => setToolArgs({ ...toolArgs, [key]: e.target.value })}
              className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              required={isRequired}
            >
              <option value="">Seleccionar...</option>
              {property.enum.map((option: string) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          );
        }
        return (
          <input
            type="text"
            value={toolArgs[key] || ''}
            onChange={(e) => setToolArgs({ ...toolArgs, [key]: e.target.value })}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={property.description || `Ingrese ${key}`}
            required={isRequired}
          />
        );
      
      case 'number':
        return (
          <input
            type="number"
            value={toolArgs[key] || ''}
            onChange={(e) => setToolArgs({ ...toolArgs, [key]: parseFloat(e.target.value) || 0 })}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={property.description || `Ingrese ${key}`}
            required={isRequired}
          />
        );
      
      case 'boolean':
        return (
          <label className="flex items-center mt-1">
            <input
              type="checkbox"
              checked={toolArgs[key] || false}
              onChange={(e) => setToolArgs({ ...toolArgs, [key]: e.target.checked })}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              {property.description || key}
            </span>
          </label>
        );
      
      default:
        return (
          <textarea
            value={typeof toolArgs[key] === 'object' ? JSON.stringify(toolArgs[key], null, 2) : (toolArgs[key] || '')}
            onChange={(e) => {
              try {
                const value = e.target.value;
                setToolArgs({ ...toolArgs, [key]: value.startsWith('{') || value.startsWith('[') ? JSON.parse(value) : value });
              } catch {
                setToolArgs({ ...toolArgs, [key]: e.target.value });
              }
            }}
            rows={3}
            className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={property.description || `Ingrese ${key} (JSON si es objeto/array)`}
            required={isRequired}
          />
        );
    }
  };

  if (!activeConnection) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Selecciona una conexión activa para ver las herramientas disponibles.
        </p>
      </div>
    );
  }

  if (tools.length === 0) {
    return (
      <div className="text-center py-8">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No hay herramientas disponibles
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          El servidor conectado no expone ninguna herramienta.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Lista de herramientas */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Herramientas Disponibles ({tools.length})
        </h3>
        <div className="space-y-3">
          {tools.map((tool) => (
            <div
              key={tool.name}
              onClick={() => handleToolSelect(tool)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedTool?.name === tool.name
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <h4 className="font-medium text-gray-900 dark:text-white">
                {tool.name}
              </h4>
              {tool.description && (
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {tool.description}
                </p>
              )}
              {tool.inputSchema.required && tool.inputSchema.required.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Parámetros requeridos: {tool.inputSchema.required.join(', ')}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Panel de ejecución */}
      <div>
        {selectedTool ? (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Ejecutar: {selectedTool.name}
            </h3>
            
            {selectedTool.description && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                {selectedTool.description}
              </p>
            )}

            {/* Formulario de parámetros */}
            {selectedTool.inputSchema.properties && Object.keys(selectedTool.inputSchema.properties).length > 0 && (
              <div className="space-y-4 mb-6">
                <h4 className="font-medium text-gray-900 dark:text-white">Parámetros</h4>
                {Object.entries(selectedTool.inputSchema.properties).map(([key, property]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {key}
                      {selectedTool.inputSchema.required?.includes(key) && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    {renderInputField(key, property)}
                    {(property as any).description && (
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {(property as any).description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Botón de ejecución */}
            <button
              onClick={handleExecuteTool}
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              {loading ? 'Ejecutando...' : 'Ejecutar Herramienta'}
            </button>

            {/* Resultados */}
            {error && (
              <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <h4 className="font-medium text-red-800 dark:text-red-300 mb-2">Error</h4>
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}

            {result && (
              <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">Resultado</h4>
                <div className="space-y-2">
                  {result.content.map((content, index) => (
                    <div key={index} className="text-sm">
                      {content.type === 'text' && (
                        <pre className="whitespace-pre-wrap text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/40 p-2 rounded">
                          {content.text}
                        </pre>
                      )}
                      {content.type === 'image' && (
                        <div>
                          <p className="text-green-700 dark:text-green-300 mb-1">Imagen:</p>
                          <img 
                            src={`data:${content.mimeType};base64,${content.data}`} 
                            alt="Tool result" 
                            className="max-w-full h-auto rounded"
                          />
                        </div>
                      )}
                      {content.type === 'resource' && (
                        <div>
                          <p className="text-green-700 dark:text-green-300">Recurso: {content.text}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                {result.isError && (
                  <p className="text-xs text-red-600 dark:text-red-400 mt-2">
                    ⚠️ La herramienta reportó un error
                  </p>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
              Selecciona una herramienta para ejecutarla.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
