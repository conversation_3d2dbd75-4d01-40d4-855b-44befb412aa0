const express = require('express');
const { createServer } = require('http');
const { WebSocketServer } = require('ws');
const { spawn } = require('child_process');
const cors = require('cors');

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

app.use(cors());
app.use(express.json());

// Almacenar procesos activos
const activeProcesses = new Map();

wss.on('connection', (ws) => {
  console.log('Nueva conexión WebSocket establecida');
  
  let currentProcess = null;
  let processId = null;

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'start_process':
          handleStartProcess(message, ws);
          break;
        case 'stdin':
          handleStdin(message, ws);
          break;
        case 'stop_process':
          handleStopProcess(ws);
          break;
        default:
          console.warn('Tipo de mensaje desconocido:', message.type);
      }
    } catch (error) {
      console.error('Error procesando mensaje WebSocket:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message
      }));
    }
  });

  ws.on('close', () => {
    console.log('Conexión WebSocket cerrada');
    if (currentProcess) {
      handleStopProcess(ws);
    }
  });

  function handleStartProcess(message, ws) {
    try {
      const { command, args = [], env = {} } = message;
      
      if (!command) {
        throw new Error('Comando requerido');
      }

      console.log(`Iniciando proceso: ${command} ${args.join(' ')}`);

      // Crear proceso
      currentProcess = spawn(command, args, {
        env: { ...process.env, ...env },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      processId = Date.now().toString();
      activeProcesses.set(processId, currentProcess);

      // Configurar listeners del proceso
      currentProcess.stdout.on('data', (data) => {
        ws.send(JSON.stringify({
          type: 'stdout',
          data: data.toString()
        }));
      });

      currentProcess.stderr.on('data', (data) => {
        ws.send(JSON.stringify({
          type: 'stderr',
          data: data.toString()
        }));
      });

      currentProcess.on('error', (error) => {
        console.error('Error del proceso:', error);
        ws.send(JSON.stringify({
          type: 'error',
          message: error.message
        }));
      });

      currentProcess.on('exit', (code, signal) => {
        console.log(`Proceso terminado con código ${code}, señal ${signal}`);
        ws.send(JSON.stringify({
          type: 'exit',
          code,
          signal
        }));
        
        if (processId) {
          activeProcesses.delete(processId);
        }
        currentProcess = null;
        processId = null;
      });

      // Confirmar que el proceso se inició
      ws.send(JSON.stringify({
        type: 'process_started',
        processId
      }));

    } catch (error) {
      console.error('Error iniciando proceso:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message
      }));
    }
  }

  function handleStdin(message, ws) {
    if (!currentProcess) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'No hay proceso activo'
      }));
      return;
    }

    try {
      currentProcess.stdin.write(message.data);
    } catch (error) {
      console.error('Error escribiendo a stdin:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message
      }));
    }
  }

  function handleStopProcess(ws) {
    if (currentProcess) {
      try {
        currentProcess.kill('SIGTERM');
        
        // Forzar terminación después de 5 segundos
        setTimeout(() => {
          if (currentProcess && !currentProcess.killed) {
            currentProcess.kill('SIGKILL');
          }
        }, 5000);
        
      } catch (error) {
        console.error('Error terminando proceso:', error);
      }
    }
  }
});

// Endpoint de salud
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    activeProcesses: activeProcesses.size,
    timestamp: new Date().toISOString()
  });
});

// Endpoint para listar procesos activos
app.get('/processes', (req, res) => {
  const processes = Array.from(activeProcesses.keys()).map(id => ({
    id,
    pid: activeProcesses.get(id)?.pid
  }));
  
  res.json({ processes });
});

// Endpoint para terminar un proceso específico
app.delete('/processes/:id', (req, res) => {
  const { id } = req.params;
  const process = activeProcesses.get(id);
  
  if (!process) {
    return res.status(404).json({ error: 'Proceso no encontrado' });
  }
  
  try {
    process.kill('SIGTERM');
    activeProcesses.delete(id);
    res.json({ message: 'Proceso terminado' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Manejo de cierre graceful
process.on('SIGINT', () => {
  console.log('Cerrando servidor proxy...');
  
  // Terminar todos los procesos activos
  for (const [id, proc] of activeProcesses) {
    try {
      proc.kill('SIGTERM');
    } catch (error) {
      console.error(`Error terminando proceso ${id}:`, error);
    }
  }
  
  server.close(() => {
    console.log('Servidor proxy cerrado');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Servidor proxy MCP ejecutándose en puerto ${PORT}`);
  console.log(`WebSocket disponible en ws://localhost:${PORT}`);
  console.log(`API REST disponible en http://localhost:${PORT}`);
});
