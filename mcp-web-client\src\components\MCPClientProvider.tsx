'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useMCPClient } from '@/hooks/useMCPClient';
import {
  MCPConnection,
  TransportConfig,
  CallToolRequest,
  CallToolResult,
  ReadResourceRequest,
  ReadResourceResult,
  GetPromptRequest,
  GetPromptResult,
  LogEntry
} from '@/types/mcp';

interface MCPClientContextType {
  // Estado
  connections: MCPConnection[];
  activeConnection?: string;
  logs: LogEntry[];
  
  // Acciones
  createConnection: (name: string, config: TransportConfig) => Promise<string>;
  connectToServer: (connectionId: string) => Promise<void>;
  disconnectFromServer: (connectionId: string) => Promise<void>;
  removeConnection: (connectionId: string) => Promise<void>;
  setActiveConnection: (connectionId: string | undefined) => void;
  
  // Operaciones MCP
  callTool: (connectionId: string, request: CallToolRequest) => Promise<CallToolResult>;
  readResource: (connectionId: string, request: ReadResourceRequest) => Promise<ReadResourceResult>;
  getPrompt: (connectionId: string, request: GetPromptRequest) => Promise<GetPromptResult>;
  
  // Utilidades
  clearLogs: () => void;
  getActiveConnection: () => MCPConnection | undefined;
  getConnectedClients: () => MCPConnection[];
  isConnected: (connectionId: string) => boolean;
}

const MCPClientContext = createContext<MCPClientContextType | undefined>(undefined);

interface MCPClientProviderProps {
  children: ReactNode;
}

export function MCPClientProvider({ children }: MCPClientProviderProps) {
  const mcpClient = useMCPClient();

  return (
    <MCPClientContext.Provider value={mcpClient}>
      {children}
    </MCPClientContext.Provider>
  );
}

export function useMCPClientContext() {
  const context = useContext(MCPClientContext);
  if (context === undefined) {
    throw new Error('useMCPClientContext must be used within a MCPClientProvider');
  }
  return context;
}
