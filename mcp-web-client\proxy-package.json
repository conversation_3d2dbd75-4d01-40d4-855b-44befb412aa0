{"name": "mcp-proxy-server", "version": "1.0.0", "description": "Servidor proxy para conexiones MCP STDIO desde el navegador", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mcp", "model-context-protocol", "proxy", "websocket"], "author": "MCP Web Client", "license": "MIT"}