import { BaseMCPTransport } from '../base-client';
import { MCPMessage, TransportConfig } from '@/types/mcp';

export interface HttpTransportConfig extends TransportConfig {
  type: 'http' | 'streamable-http';
  url: string;
  headers?: Record<string, string>;
}

export class HttpTransport extends BaseMCPTransport {
  private config: HttpTransportConfig;
  private sessionId: string | null = null;
  private protocolVersion: string = '2025-03-26';
  private sseStream: EventSource | null = null;
  private supportsSSE: boolean = false;

  constructor(config: HttpTransportConfig) {
    super();
    this.config = config;
  }

  async connect(): Promise<void> {
    if (this.connecting || this.connected) {
      return;
    }

    this.connecting = true;

    try {
      // Detectar capacidades del servidor
      await this.detectServerCapabilities();
      this.handleConnect();
    } catch (error) {
      this.connecting = false;
      throw error;
    }
  }

  private async detectServerCapabilities(): Promise<void> {
    try {
      // Intentar POST primero (Streamable HTTP)
      const testResponse = await fetch(this.config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream',
          'MCP-Protocol-Version': this.protocolVersion,
          ...this.config.headers
        },
        body: JSON.stringify({
          jsonrpc: "2.0",
          method: "ping",
          id: "capability-test"
        })
      });

      if (testResponse.ok) {
        // El servidor soporta Streamable HTTP
        console.log('Server supports Streamable HTTP transport');
        return;
      }

      // Si POST falla, intentar GET para SSE (backward compatibility)
      const sseResponse = await fetch(this.config.url, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          ...this.config.headers
        }
      });

      if (sseResponse.ok && sseResponse.headers.get('Content-Type')?.includes('text/event-stream')) {
        this.supportsSSE = true;
        console.log('Server supports legacy SSE transport');
        await this.setupSSEStream();
        return;
      }

      throw new Error('Server does not support any known MCP transport');

    } catch (error) {
      throw new Error(`Failed to detect server capabilities: ${error}`);
    }
  }

  private async setupSSEStream(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.sseStream = new EventSource(this.config.url);

      const timeout = setTimeout(() => {
        this.cleanup();
        reject(new Error('Timeout establishing SSE stream'));
      }, 10000);

      this.sseStream.onopen = () => {
        console.log('SSE stream established');
      };

      this.sseStream.addEventListener('endpoint', (event: any) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Received endpoint event:', data);
          clearTimeout(timeout);
          resolve();
        } catch (error) {
          clearTimeout(timeout);
          reject(new Error('Invalid endpoint event'));
        }
      });

      this.sseStream.addEventListener('message', (event: any) => {
        try {
          const message: MCPMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      });

      this.sseStream.onerror = (error) => {
        clearTimeout(timeout);
        if (this.sseStream?.readyState === EventSource.CLOSED) {
          this.handleDisconnect();
        } else {
          reject(new Error('SSE stream error'));
        }
      };
    });
  }

  async send(message: MCPMessage): Promise<void> {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    try {
      const response = await fetch(this.config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream',
          'MCP-Protocol-Version': this.protocolVersion,
          ...(this.sessionId && { 'Mcp-Session-Id': this.sessionId }),
          ...this.config.headers
        },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
        if (response.status === 404 && this.sessionId) {
          // Sesión expirada, reinicializar
          this.sessionId = null;
          throw new Error('Session expired, please reconnect');
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Verificar si el servidor devolvió un session ID
      const sessionId = response.headers.get('Mcp-Session-Id');
      if (sessionId && !this.sessionId) {
        this.sessionId = sessionId;
      }

      const contentType = response.headers.get('Content-Type');

      if (contentType?.includes('text/event-stream')) {
        // Respuesta streaming
        await this.handleStreamingResponse(response);
      } else if (contentType?.includes('application/json')) {
        // Respuesta JSON única
        const responseData = await response.json();
        if (responseData) {
          this.handleMessage(responseData);
        }
      } else if (response.status === 202) {
        // Accepted - no response body expected
        return;
      }

    } catch (error) {
      throw new Error(`Failed to send message: ${error}`);
    }
  }

  private async handleStreamingResponse(response: Response): Promise<void> {
    if (!response.body) {
      throw new Error('No response body for streaming response');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        
        // Procesar eventos SSE
        const events = this.parseSSEEvents(buffer);
        buffer = events.remainder;

        for (const event of events.events) {
          if (event.data) {
            try {
              const message: MCPMessage = JSON.parse(event.data);
              this.handleMessage(message);
            } catch (error) {
              console.error('Error parsing streaming message:', error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  private parseSSEEvents(buffer: string): { events: Array<{data?: string, id?: string}>, remainder: string } {
    const events: Array<{data?: string, id?: string}> = [];
    const lines = buffer.split('\n');
    let remainder = '';
    let currentEvent: {data?: string, id?: string} = {};
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      
      if (line === '') {
        // Fin del evento
        if (currentEvent.data !== undefined) {
          events.push(currentEvent);
        }
        currentEvent = {};
      } else if (line.startsWith('data: ')) {
        currentEvent.data = line.substring(6);
      } else if (line.startsWith('id: ')) {
        currentEvent.id = line.substring(4);
      }
      
      i++;
    }

    // Si la última línea no está completa, mantenerla en el buffer
    if (lines.length > 0 && !buffer.endsWith('\n')) {
      remainder = lines[lines.length - 1];
    }

    return { events, remainder };
  }

  async disconnect(): Promise<void> {
    // Enviar DELETE para terminar sesión si tenemos session ID
    if (this.sessionId) {
      try {
        await fetch(this.config.url, {
          method: 'DELETE',
          headers: {
            'Mcp-Session-Id': this.sessionId,
            ...this.config.headers
          }
        });
      } catch (error) {
        console.warn('Failed to terminate session:', error);
      }
    }

    this.cleanup();
    this.handleDisconnect();
  }

  private cleanup(): void {
    if (this.sseStream) {
      this.sseStream.close();
      this.sseStream = null;
    }
    this.sessionId = null;
    this.supportsSSE = false;
  }
}

// Función helper para crear transporte HTTP
export function createHttpTransport(config: Omit<HttpTransportConfig, 'type'>): HttpTransport {
  return new HttpTransport({
    ...config,
    type: 'streamable-http'
  });
}

// Función para detectar automáticamente el tipo de transporte
export async function detectTransportType(url: string): Promise<'streamable-http' | 'sse' | 'unsupported'> {
  try {
    // Intentar Streamable HTTP primero
    const postResponse = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: JSON.stringify({
        jsonrpc: "2.0",
        method: "ping",
        id: "transport-detection"
      })
    });

    if (postResponse.ok) {
      return 'streamable-http';
    }

    // Intentar SSE legacy
    const getResponse = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    });

    if (getResponse.ok && getResponse.headers.get('Content-Type')?.includes('text/event-stream')) {
      return 'sse';
    }

    return 'unsupported';

  } catch (error) {
    return 'unsupported';
  }
}

// Configuraciones predefinidas
export const HTTP_PRESETS = {
  local: (port: number = 3000, path: string = '/mcp') => createHttpTransport({
    url: `http://localhost:${port}${path}`
  }),
  
  remote: (host: string, port: number = 3000, path: string = '/mcp', secure: boolean = false) => 
    createHttpTransport({
      url: `${secure ? 'https' : 'http'}://${host}:${port}${path}`
    }),
  
  custom: (url: string, headers?: Record<string, string>) => createHttpTransport({
    url,
    headers
  })
};
